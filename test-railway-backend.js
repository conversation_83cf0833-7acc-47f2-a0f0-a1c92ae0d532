const axios = require('axios');

const RAILWAY_URL = 'https://op-market-research-tool-production-93d7.up.railway.app';

async function testRailwayBackend() {
  console.log('Testing Railway Backend...\n');
  
  // Test health endpoint
  try {
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${RAILWAY_URL}/health`);
    console.log('✅ Health check passed:', healthResponse.data);
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
    return;
  }
  
  // Test Gemini endpoint
  try {
    console.log('\n2. Testing Gemini endpoint...');
    const geminiResponse = await axios.post(`${RAILWAY_URL}/api/gemini`, {
      prompt: 'What is market research?',
      model: 'gemini-1.5-pro',
      thinkingMode: false
    });
    console.log('✅ Gemini API response:', {
      success: geminiResponse.data.success,
      model: geminiResponse.data.model,
      textPreview: geminiResponse.data.text?.substring(0, 100) + '...',
      mock: geminiResponse.data.mock || false
    });
  } catch (error) {
    console.log('❌ Gemini API failed:', error.response?.data || error.message);
  }
  
  // Test OpenAI endpoint
  try {
    console.log('\n3. Testing OpenAI endpoint...');
    const openaiResponse = await axios.post(`${RAILWAY_URL}/api/openai`, {
      prompt: 'What is market research?',
      model: 'gpt-4o',
      thinkingMode: false
    });
    console.log('✅ OpenAI API response:', {
      success: openaiResponse.data.success,
      model: openaiResponse.data.model,
      textPreview: openaiResponse.data.text?.substring(0, 100) + '...',
      mock: openaiResponse.data.mock || false
    });
  } catch (error) {
    console.log('❌ OpenAI API failed:', error.response?.data || error.message);
  }
  
  // Test DeepSeek endpoint
  try {
    console.log('\n4. Testing DeepSeek endpoint...');
    const deepseekResponse = await axios.post(`${RAILWAY_URL}/api/deepseek`, {
      prompt: 'What is market research?',
      model: 'deepseek-chat',
      thinkingMode: false
    });
    console.log('✅ DeepSeek API response:', {
      success: deepseekResponse.data.success,
      model: deepseekResponse.data.model,
      textPreview: deepseekResponse.data.text?.substring(0, 100) + '...',
      mock: deepseekResponse.data.mock || false
    });
  } catch (error) {
    console.log('❌ DeepSeek API failed:', error.response?.data || error.message);
  }
  
  console.log('\n🎉 Railway backend testing completed!');
  console.log('\nNote: If you see "mock: true" responses, it means no API keys are configured on Railway.');
  console.log('The backend is working correctly and will use real APIs when keys are added.');
}

testRailwayBackend().catch(console.error);
