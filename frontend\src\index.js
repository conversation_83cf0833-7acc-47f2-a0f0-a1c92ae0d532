import React, { useState, useRef, useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter as Router, Routes, Route, NavLink, Navigate, Link } from 'react-router-dom';
import QuestionnaireBuilder from './components/QuestionnaireBuilder';
import ResponseViewer from './components/ResponseViewer';
import '../style.css';

// Import new components
import Home from './components/Home';
import ClientAcquisition from './components/ClientAcquisition';
import CustomerRetention from './components/CustomerRetention';
import Tools from './components/Tools';
import MarketResearch from './components/MarketResearch';
import CompetitionAnalysis from './components/CompetitionAnalysis';
import StrategyPage from './components/StrategyPage';
import ResponseView from './components/ResponseView';
import SupabaseTest from './components/SupabaseTest';
import ResponseDebug from './components/ResponseDebug';
import PromptManagement from './components/PromptManagement';
import CompanyProfile from './components/CompanyProfile.jsx';

// Import auth components
import Login from './components/Login';
import Signup from './components/Signup';
import ForgotPassword from './components/ForgotPassword';
import ResetPassword from './components/ResetPassword';
import UserProfile from './components/UserProfile';
import ProtectedRoute from './components/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';
import { AuthProvider, useAuth } from './context/AuthContext';

// Header component that only shows when user is logged in
function AppHeader() {
  const { user, signOut } = useAuth();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  
  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownRef]);
  
  return (
    <header className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8 flex flex-col items-center text-center">
        <div className="w-full flex justify-end mb-2">
          <div className="px-4 py-2 bg-gray-100 rounded-lg text-gray-700 text-sm mr-2">
            <span className="font-medium"></span> {user.email}
          </div>
          <div className="flex space-x-2">
            <div className="relative" ref={dropdownRef}>
              <button 
                className="px-4 py-2 bg-gray-100 rounded-lg text-gray-700 text-sm hover:bg-gray-200 transition-colors flex items-center"
                onClick={() => setDropdownOpen(!dropdownOpen)}
              >
                Profile
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={dropdownOpen ? "M5 15l7-7 7 7" : "M19 9l-7 7-7-7"} />
                </svg>
              </button>
              
              {dropdownOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                  <div className="py-1">
                    <Link 
                      to="/profile"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setDropdownOpen(false)}
                    >
                      User Profile
                    </Link>
                    <Link 
                      to="/company-profile"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setDropdownOpen(false)}
                    >
                      Company Profile
                    </Link>
                  </div>
                </div>
              )}
            </div>
            <button 
              className="px-4 py-2 bg-gray-100 rounded-lg text-gray-700 text-sm hover:bg-gray-200 transition-colors"
              onClick={signOut}
            >
              Logout
            </button>
          </div>
        </div>
        <h1 className="text-3xl md:text-3xl raleway-title mb-10">Omega Praxis - Marketing Strategy</h1>

        <nav className="mt-2">
          <ul className="flex border-b mb-6">
            <li>
              <NavLink to="/" 
                className={({ isActive }) => 
                  `py-2 px-4 mr-4 raleway-menu ${isActive 
                    ? 'border-b-2 border-blue-500 text-blue-600' 
                    : 'text-gray-500 hover:text-blue-400'}`
                }
                end
              >
                Home
              </NavLink>
            </li>
            <li>
              <NavLink to="/market-research" 
                className={({ isActive }) => 
                  `py-2 px-4 mr-4 raleway-menu ${isActive 
                    ? 'border-b-2 border-blue-500 text-blue-600' 
                    : 'text-gray-500 hover:text-blue-400'}`
                }
              >
                Market Research
              </NavLink>
            </li>
            <li>
              <NavLink to="/competition-analysis" 
                className={({ isActive }) => 
                  `py-2 px-4 mr-4 raleway-menu ${isActive 
                    ? 'border-b-2 border-blue-500 text-blue-600' 
                    : 'text-gray-500 hover:text-blue-400'}`
                }
              >
                Competition Analysis
              </NavLink>
            </li>
            <li>
              <NavLink to="/client-acquisition" 
                className={({ isActive }) => 
                  `py-2 px-4 mr-4 raleway-menu ${isActive 
                    ? 'border-b-2 border-blue-500 text-blue-600' 
                    : 'text-gray-500 hover:text-blue-400'}`
                }
              >
                Client Acquisition
              </NavLink>
            </li>
            <li>
              <NavLink to="/customer-retention" 
                className={({ isActive }) => 
                  `py-2 px-4 mr-4 raleway-menu ${isActive 
                    ? 'border-b-2 border-blue-500 text-blue-600' 
                    : 'text-gray-500 hover:text-blue-400'}`
                }
              >
                Customer Retention
              </NavLink>
            </li>
            <li>
              <NavLink to="/tools" 
                className={({ isActive }) => 
                  `py-2 px-4 mr-4 raleway-menu ${isActive 
                    ? 'border-b-2 border-blue-500 text-blue-600' 
                    : 'text-gray-500 hover:text-blue-400'}`
                }
              >
                Tools
              </NavLink>
            </li>
            <li>
              <NavLink to="/prompts" 
                className={({ isActive }) => 
                  `py-2 px-4 mr-4 raleway-menu ${isActive 
                    ? 'border-b-2 border-blue-500 text-blue-600' 
                    : 'text-gray-500 hover:text-blue-400'}`
                }
              >
                Prompts
              </NavLink>
            </li>
          </ul>
        </nav>
      </div>
    </header>
  );
}

// Main App component with conditional header
function App() {
  const { user, loading, error, fallbackMode } = useAuth();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Show fallback mode notice */}
      {fallbackMode && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Running in offline mode. Some features may be limited.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Only show header when user is logged in */}
      {user && <AppHeader />}
      
      <main className="flex justify-center items-center">
        <div className="max-w-7xl w-full mx-auto py-6 sm:px-6 lg:px-8 flex justify-center">
          <div className="w-full max-w-4xl">
            <Routes>
              {/* Public routes - no header shown */}
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<Signup />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              <Route path="/reset-password" element={<ResetPassword />} />
              <Route path="/supabase-test" element={<SupabaseTest />} />
              <Route path="/response-debug" element={<ResponseDebug />} />
              
              {/* Protected routes - header shown */}
              <Route element={<ProtectedRoute />}>
                <Route path="/" element={<Home />} />
                <Route path="/client-acquisition" element={<ClientAcquisition />} />
                <Route path="/customer-retention" element={<CustomerRetention />} />
                <Route path="/market-research" element={<MarketResearch />} />
                <Route path="/competition-analysis" element={<CompetitionAnalysis />} />
                <Route path="/tools" element={<Tools />} />
                <Route path="/strategy" element={<StrategyPage />} />
                <Route path="/responses" element={<ResponseView />} />
                <Route path="/profile" element={<UserProfile />} />
                <Route path="/company-profile" element={<CompanyProfile />} />
                <Route path="/prompts" element={<PromptManagement />} />
              </Route>
              
              {/* Fallback route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </div>
        </div>
      </main>
    </div>
  );
}

// Render the app
const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <ErrorBoundary>
      <Router>
        <AuthProvider>
          <App />
        </AuthProvider>
      </Router>
    </ErrorBoundary>
  </React.StrictMode>
);
