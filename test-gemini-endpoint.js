const axios = require('axios');

async function testGeminiEndpoint() {
  try {
    console.log('Testing Gemini API endpoint...');
    
    const response = await axios.post(
      'http://localhost:3000/api/gemini',
      {
        prompt: 'Test prompt for Gemini API',
        model: 'gemini-pro'
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);
    
    return response.data;
  } catch (error) {
    console.error('Error testing Gemini API endpoint:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testGeminiEndpoint();
