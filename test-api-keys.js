// Test script for verifying API keys
const dotenv = require('dotenv');
const path = require('path');
const { OpenAI } = require('openai');
const { GoogleGenerativeAI } = require('@google/generative-ai');

// Load environment variables
console.log('Loading environment variables...');
const result = dotenv.config({ path: path.resolve(__dirname, '.env') });

if (result.error) {
  console.error('Error loading .env file:', result.error);
  process.exit(1);
} else {
  console.log('.env file loaded successfully');
}

// Normalize environment variables
process.env.GEMINI_API_KEY = process.env.GEMINI_API_KEY || process.env.google_api_key;
process.env.OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.openai_api_key;
process.env.DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || process.env.deepseek_api_key;

// Display API key availability and first few characters for debugging
console.log('\nAPI Key Status:');

// Helper function to safely display part of the API key
function displayPartialKey(key) {
  if (!key) return 'Not available';
  if (key.length <= 5) return 'Available (too short, might be invalid)';
  return `Available (starts with: ${key.substring(0, 5)}...)`;
}

console.log('- GEMINI_API_KEY:', displayPartialKey(process.env.GEMINI_API_KEY));
console.log('- OPENAI_API_KEY:', displayPartialKey(process.env.OPENAI_API_KEY));
console.log('- DEEPSEEK_API_KEY:', displayPartialKey(process.env.DEEPSEEK_API_KEY));

// Also check for duplicate keys that might cause confusion
console.log('\nChecking for duplicate keys:');
if (process.env.GEMINI_API_KEY === process.env.google_api_key) {
  console.log('- GEMINI_API_KEY and google_api_key are identical');
} else {
  console.log('- google_api_key:', displayPartialKey(process.env.google_api_key));
}

// Check for common API key format issues
console.log('\nAPI Key Format Check:');
if (process.env.GEMINI_API_KEY && !process.env.GEMINI_API_KEY.startsWith('AIza')) {
  console.log('- ⚠️ GEMINI_API_KEY format looks incorrect. Google API keys typically start with "AIza"');
}
if (process.env.OPENAI_API_KEY && !process.env.OPENAI_API_KEY.startsWith('sk-')) {
  console.log('- ⚠️ OPENAI_API_KEY format looks incorrect. OpenAI API keys typically start with "sk-"');
}

// Test prompt
const TEST_PROMPT = 'Generate a short marketing tagline for an eco-friendly water bottle.';

// Function to test Gemini API key
async function testGeminiApiKey() {
  console.log('\n--- Testing Gemini API Key ---');
  
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    console.log('❌ Gemini API key not found in environment variables');
    return false;
  }
  
  // Debug info
  console.log(`API Key length: ${apiKey.length} characters`);
  console.log(`API Key format check: ${apiKey.startsWith('AIza') ? 'Correct prefix (AIza)' : 'Incorrect prefix'}`);
  
  // Check for common issues
  if (apiKey.includes(' ')) {
    console.log('⚠️ Warning: API key contains spaces, which may cause issues');
  }
  if (apiKey.includes('"') || apiKey.includes('\'')) {
    console.log('⚠️ Warning: API key contains quotes, which will cause issues');
  }
  
  // Also check if there's a conflict with google_api_key
  if (process.env.google_api_key && process.env.google_api_key !== apiKey) {
    console.log('⚠️ Warning: google_api_key is different from GEMINI_API_KEY, which may cause conflicts');
  }
  
  try {
    console.log('Initializing Gemini API with key...');
    console.log(`Using API key: ${apiKey.substring(0, 5)}...${apiKey.substring(apiKey.length - 3)}`);
    
    const genAI = new GoogleGenerativeAI(apiKey);
    console.log('GoogleGenerativeAI client initialized');
    
    // Focus on the most likely model to work with current Gemini API
    try {
      console.log('Trying model: gemini-1.5-pro...');
      const model = genAI.getGenerativeModel({ model: 'gemini-1.5-pro' });
      
      console.log('Sending test prompt to Gemini API...');
      const result = await model.generateContent(TEST_PROMPT);
      const response = await result.response;
      const text = response.text();
      
      console.log('✅ Gemini API key is valid!');
      console.log('Response:', text);
      return true;
    } catch (err) {
      console.log('Error with Gemini API:', err.message);
      throw err; // Re-throw to be caught by the outer catch block
    }
    
    // If we get here, all models failed
    throw modelError || new Error('All Gemini models failed');
  } catch (error) {
    console.log('❌ Gemini API key validation failed');
    console.log('Error:', error.message);
    console.log('Error details:', JSON.stringify(error, null, 2));
    
    if (error.message.includes('API key not valid')) {
      console.log('\nTroubleshooting tips:');
      console.log('1. Verify that your API key is correctly copied from the Google AI Studio (https://makersuite.google.com/)');
      console.log('2. Ensure there are no extra spaces or characters in your API key');
      console.log('3. Check if your API key has been revoked or expired');
      console.log('4. Try generating a new API key from the Google AI Studio');
      console.log('5. Make sure your .env file is correctly formatted (GEMINI_API_KEY=your_key_here without quotes)');
      console.log('6. Verify that you have enabled the Gemini API in your Google Cloud Console');
      console.log('7. Check if your API key has the necessary permissions for the Gemini API');
      console.log('8. Try using a different API key from Google AI Studio');
    } else if (error.message.includes('not found')) {
      console.log('\nTroubleshooting tips:');
      console.log('1. Verify that you have enabled the Gemini API in your Google Cloud Console');
      console.log('2. Check if your API key has access to the Gemini models');
      console.log('3. The model name might have changed - try generating a new API key from Google AI Studio');
      console.log('4. Visit https://ai.google.dev/ for the latest documentation on available models');
    }
    
    return false;
  }
}

// Function to test OpenAI API key
async function testOpenAIApiKey() {
  console.log('\n--- Testing OpenAI API Key ---');
  
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    console.log('❌ OpenAI API key not found in environment variables');
    return false;
  }
  
  // Debug info
  console.log(`API Key length: ${apiKey.length} characters`);
  console.log(`API Key format check: ${apiKey.startsWith('sk-') ? 'Correct prefix (sk-)' : 'Incorrect prefix'}`);
  
  try {
    console.log('Initializing OpenAI API with key...');
    // Create OpenAI client with explicit configuration
    const openai = new OpenAI({
      apiKey: apiKey,
      dangerouslyAllowBrowser: false,
      maxRetries: 0
    });
    
    console.log('OpenAI client initialized successfully');
    console.log('Sending test prompt to OpenAI API...');
    
    // Log the exact request being sent
    const requestParams = {
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: 'You are a helpful marketing assistant.' },
        { role: 'user', content: TEST_PROMPT }
      ],
      max_tokens: 50
    };
    console.log('Request parameters:', JSON.stringify(requestParams, null, 2));
    
    const completion = await openai.chat.completions.create(requestParams);
    
    console.log('✅ OpenAI API key is valid!');
    console.log('Response:', completion.choices[0].message.content);
    return true;
  } catch (error) {
    console.log('❌ OpenAI API key validation failed');
    console.log('Error:', error.message);
    console.log('Error details:', JSON.stringify(error, null, 2));
    
    if (error.status === 401) {
      console.log('\nTroubleshooting tips:');
      console.log('1. Verify that your API key is correctly copied from the OpenAI dashboard (https://platform.openai.com/api-keys)');
      console.log('2. Ensure there are no extra spaces or characters in your API key');
      console.log('3. Check if your API key has been revoked or expired');
      console.log('4. Try generating a new API key from the OpenAI dashboard');
      console.log('5. Make sure your .env file is correctly formatted (OPENAI_API_KEY=your_key_here without quotes)');
      console.log('6. Verify that your OpenAI account has a valid payment method if using paid models');
      console.log('7. If using a project-scoped API key, ensure it has access to the requested model');
    }
    
    return false;
  }
}

// Function to test DeepSeek API key (mock test since we don't have actual implementation)
async function testDeepSeekApiKey() {
  console.log('\n--- Testing DeepSeek API Key ---');
  
  const apiKey = process.env.DEEPSEEK_API_KEY;
  if (!apiKey) {
    console.log('❌ DeepSeek API key not found in environment variables');
    return false;
  }
  
  console.log('✅ DeepSeek API key is available in environment variables');
  console.log('Note: Actual API validation is not implemented. This is just checking for key presence.');
  return true;
}

// Main function to run all tests
async function runTests() {
  console.log('Starting API key validation tests...');
  
  // Test all API keys
  const geminiResult = await testGeminiApiKey();
  const openaiResult = await testOpenAIApiKey();
  const deepseekResult = await testDeepSeekApiKey();
  
  // Summary
  console.log('\n--- Test Summary ---');
  console.log('Gemini API:', geminiResult ? '✅ Valid' : '❌ Invalid or unavailable');
  console.log('OpenAI API:', openaiResult ? '✅ Valid' : '❌ Invalid or unavailable');
  console.log('DeepSeek API:', deepseekResult ? '✅ Available' : '❌ Unavailable');
  
  if (!geminiResult || !openaiResult || !deepseekResult) {
    console.log('\nSome API keys are invalid or missing. Please check your .env file and make sure the keys are correctly formatted:');
    console.log('GEMINI_API_KEY=your_gemini_key_here');
    console.log('OPENAI_API_KEY=your_openai_key_here');
    console.log('DEEPSEEK_API_KEY=your_deepseek_key_here');
  } else {
    console.log('\nAll API keys are valid and available! Your LLM integrations should work correctly.');
  }
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
});
