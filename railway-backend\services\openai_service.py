import os
import httpx
import json
from typing import Dict, Any, Optional
import asyncio

class OpenAIService:
    """Service for handling OpenAI API interactions"""
    
    def __init__(self):
        self.api_key = os.environ.get("OPENAI_API_KEY")
        self.base_url = "https://api.openai.com/v1"
        
    async def generate_response(
        self, 
        prompt: str, 
        model: str = "gpt-4o", 
        thinking_mode: bool = False
    ) -> Dict[str, Any]:
        """Generate response using OpenAI API"""
        
        if not self.api_key:
            return {
                "text": f"Mock response from OpenAI {model}: {prompt[:100]}...",
                "answer": f"Mock response from OpenAI {model}: {prompt[:100]}...",
                "model": model,
                "success": True,
                "mock": True,
                "error": "No API key configured"
            }
        
        try:
            # Prepare the final prompt based on thinking mode
            final_prompt = self._prepare_prompt(prompt, thinking_mode)
            
            # Prepare the request payload
            payload = {
                "model": model,
                "messages": [
                    {
                        "role": "system", 
                        "content": "You are a helpful market research and client acquisition expert. Provide detailed, actionable advice with examples when possible."
                    },
                    {
                        "role": "user", 
                        "content": final_prompt
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 2000
            }
            
            # Make the API call
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    },
                    json=payload
                )
                
                if response.status_code != 200:
                    error_detail = response.text
                    return {
                        "text": f"Error calling OpenAI API: {error_detail}",
                        "answer": f"Error calling OpenAI API: {error_detail}",
                        "model": model,
                        "success": False,
                        "error": error_detail
                    }
                
                result = response.json()
                
                # Extract the response text
                if "choices" in result and len(result["choices"]) > 0:
                    choice = result["choices"][0]
                    if "message" in choice and "content" in choice["message"]:
                        response_text = choice["message"]["content"]
                        
                        return {
                            "text": response_text,
                            "answer": response_text,  # For compatibility with frontend
                            "model": model,
                            "success": True,
                            "thinkingMode": thinking_mode
                        }
                
                # If we can't extract the response, return an error
                return {
                    "text": "No valid response from OpenAI API",
                    "answer": "No valid response from OpenAI API",
                    "model": model,
                    "success": False,
                    "error": "Invalid response format"
                }
                
        except httpx.TimeoutException:
            return {
                "text": "Request to OpenAI API timed out",
                "answer": "Request to OpenAI API timed out",
                "model": model,
                "success": False,
                "error": "Timeout"
            }
        except Exception as e:
            return {
                "text": f"Error calling OpenAI API: {str(e)}",
                "answer": f"Error calling OpenAI API: {str(e)}",
                "model": model,
                "success": False,
                "error": str(e)
            }
    
    def _prepare_prompt(self, prompt: str, thinking_mode: bool) -> str:
        """Prepare the prompt based on thinking mode"""
        
        if thinking_mode:
            return f"""When answering, please follow this structure:
1. THINKING: First, think step by step about the question. Consider different angles, relevant marketing concepts, and potential strategies. This section is for your analytical process.
2. ANSWER: Then provide your final, well-structured answer based on your thinking.

Question: {prompt}"""
        else:
            return prompt
