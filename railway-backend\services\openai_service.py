import os
import httpx
import json
from typing import Dict, Any, Optional
import asyncio

class OpenAIService:
    """Service for handling OpenAI API interactions"""
    
    def __init__(self):
        self.api_key = os.environ.get("OPENAI_API_KEY")
        self.base_url = "https://api.openai.com/v1"
        
    async def generate_response(
        self,
        prompt: str,
        model: str = "gpt-4o",
        thinking_mode: bool = False
    ) -> Dict[str, Any]:
        """Generate response using OpenAI API"""

        print(f"🔍 OpenAI Service Debug:")
        print(f"  - API Key present: {bool(self.api_key)}")
        print(f"  - API Key length: {len(self.api_key) if self.api_key else 0}")
        print(f"  - Model: {model}")
        print(f"  - Thinking mode: {thinking_mode}")
        print(f"  - Prompt length: {len(prompt)}")
        print(f"  - Prompt preview: {prompt[:200]}...")

        if not self.api_key:
            print("❌ No API key found - returning mock response")
            return {
                "text": f"Mock response from OpenAI {model}: {prompt[:100]}...",
                "answer": f"Mock response from OpenAI {model}: {prompt[:100]}...",
                "model": model,
                "success": True,
                "mock": True,
                "error": "No API key configured"
            }
        
        try:
            # Check if this is an o1 model (reasoning models)
            is_o1_model = model.startswith("o1-")

            # Prepare the final prompt based on thinking mode and model type
            final_prompt = self._prepare_prompt(prompt, thinking_mode, is_o1_model)

            # Prepare the request payload
            if is_o1_model:
                # o1 models don't support system messages or temperature
                payload = {
                    "model": model,
                    "messages": [
                        {
                            "role": "user",
                            "content": final_prompt
                        }
                    ],
                    "max_completion_tokens": 2000
                }
            else:
                # Regular models
                payload = {
                    "model": model,
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are a helpful market research and client acquisition expert. Provide detailed, actionable advice with examples when possible."
                        },
                        {
                            "role": "user",
                            "content": final_prompt
                        }
                    ],
                    "temperature": 0.7,
                    "max_tokens": 2000
                }
            
            # Make the API call
            print(f"🚀 Making OpenAI API call:")
            print(f"  - URL: {self.base_url}/chat/completions")
            print(f"  - Model: {model}")
            print(f"  - Is o1 model: {is_o1_model}")
            print(f"  - Payload keys: {list(payload.keys())}")

            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    },
                    json=payload
                )

                print(f"📡 OpenAI API Response:")
                print(f"  - Status code: {response.status_code}")
                print(f"  - Response headers: {dict(response.headers)}")

                if response.status_code != 200:
                    error_detail = response.text
                    print(f"❌ OpenAI API Error: {error_detail}")
                    return {
                        "text": f"Error calling OpenAI API: {error_detail}",
                        "answer": f"Error calling OpenAI API: {error_detail}",
                        "model": model,
                        "success": False,
                        "error": error_detail
                    }
                
                result = response.json()
                
                # Extract the response text
                if "choices" in result and len(result["choices"]) > 0:
                    choice = result["choices"][0]
                    if "message" in choice and "content" in choice["message"]:
                        response_text = choice["message"]["content"]
                        
                        return {
                            "text": response_text,
                            "answer": response_text,  # For compatibility with frontend
                            "model": model,
                            "success": True,
                            "thinkingMode": thinking_mode
                        }
                
                # If we can't extract the response, return an error
                return {
                    "text": "No valid response from OpenAI API",
                    "answer": "No valid response from OpenAI API",
                    "model": model,
                    "success": False,
                    "error": "Invalid response format"
                }
                
        except httpx.TimeoutException:
            return {
                "text": "Request to OpenAI API timed out",
                "answer": "Request to OpenAI API timed out",
                "model": model,
                "success": False,
                "error": "Timeout"
            }
        except Exception as e:
            return {
                "text": f"Error calling OpenAI API: {str(e)}",
                "answer": f"Error calling OpenAI API: {str(e)}",
                "model": model,
                "success": False,
                "error": str(e)
            }
    
    def _prepare_prompt(self, prompt: str, thinking_mode: bool, is_o1_model: bool = False) -> str:
        """Prepare the prompt based on thinking mode and model type"""

        if is_o1_model:
            # o1 models are designed for reasoning, so we include system context in the user message
            base_context = "You are a helpful market research and client acquisition expert. Provide detailed, actionable advice with examples when possible.\n\n"

            if thinking_mode:
                return f"""{base_context}Please think through this question step by step, showing your reasoning process, then provide a clear final answer.

Question: {prompt}"""
            else:
                return f"{base_context}{prompt}"
        else:
            # Regular models
            if thinking_mode:
                return f"""When answering, please follow this structure:
1. THINKING: First, think step by step about the question. Consider different angles, relevant marketing concepts, and potential strategies. This section is for your analytical process.
2. ANSWER: Then provide your final, well-structured answer based on your thinking.

Question: {prompt}"""
            else:
                return prompt
