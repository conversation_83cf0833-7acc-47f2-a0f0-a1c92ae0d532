// Simple test script for the minimal API server
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// API endpoints to test
const endpoints = [
  {
    name: 'Gemini',
    url: 'http://localhost:3001/api/gemini',
    data: { prompt: 'Generate a short marketing tagline for an eco-friendly water bottle.', model: 'gemini-1.5-pro' }
  },
  {
    name: 'OpenAI',
    url: 'http://localhost:3001/api/openai',
    data: { question: 'Generate a short marketing tagline for an eco-friendly water bottle.', model: 'gpt-3.5-turbo' }
  },
  {
    name: 'DeepSeek',
    url: 'http://localhost:3001/api/deepseek',
    data: { prompt: 'Generate a short marketing tagline for an eco-friendly water bottle.', model: 'deepseek-chat' }
  }
];

// Function to test an endpoint
async function testEndpoint(endpoint) {
  console.log(`\n--- Testing ${endpoint.name} API ---`);
  try {
    console.log(`Sending request to ${endpoint.url}...`);
    const response = await axios.post(endpoint.url, endpoint.data, {
      timeout: 5000 // 5 second timeout
    });
    console.log(`✅ ${endpoint.name} API responded successfully!`);
    console.log('Status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.log(`❌ ${endpoint.name} API request failed`);
    console.log('Error:', error.message);
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response data:', error.response.data);
    }
    return false;
  }
}

// Run tests sequentially
async function runTests() {
  console.log('Starting API server tests...');
  
  // Check if server-running.txt exists
  const serverRunningPath = path.join(__dirname, 'server-running.txt');
  if (fs.existsSync(serverRunningPath)) {
    const content = fs.readFileSync(serverRunningPath, 'utf8');
    console.log('Server status file exists:', content);
  } else {
    console.log('Server status file does not exist. Server might not be running.');
    console.log('Please start the server with: node minimal-api-server.js');
    return;
  }
  
  // Test each endpoint
  for (const endpoint of endpoints) {
    await testEndpoint(endpoint);
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Test a second request to verify the server is still running
  console.log('\n--- Testing server persistence ---');
  console.log('Sending a second request to verify the server is still running...');
  await testEndpoint(endpoints[0]);
  
  console.log('\nAll tests completed!');
}

// Run the tests
runTests();
