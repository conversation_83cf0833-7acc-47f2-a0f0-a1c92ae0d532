import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import config from '../config';
import { getQuestionnaireResponses, supabase } from '../supabase/client';
import { useAuth } from '../context/AuthContext';
import OpenAI from 'openai';

// Helper function to safely get a truncated ID
const safeGetId = (response, length = 8) => {
  if (!response) return 'Unknown';
  if (!response.id) return 'No ID';
  
  try {
    if (typeof response.id === 'string') {
      return response.id.slice(0, length) + (response.id.length > length ? '...' : '');
    } else if (typeof response.id === 'number') {
      return response.id.toString();
    } else {
      return JSON.stringify(response.id).slice(0, length) + '...';
    }
  } catch (err) {
    console.error('Error formatting ID:', err);
    return 'Error';
  }
};

// API Key Dialog Component
const ApiKeyDialog = ({ isOpen, onClose, onSave, apiKeyError }) => {
  const [apiKey, setApiKey] = useState('');
  
  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(apiKey);
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
        <h3 className="text-xl font-semibold mb-4">OpenAI API Key Required</h3>
        <p className="text-gray-600 mb-4">
          To use OpenAI models, please enter your API key. Your key is stored locally in your browser and never sent to our servers.
        </p>
        
        {apiKeyError && (
          <div className="bg-red-50 text-red-700 p-3 rounded mb-4 text-sm">
            {apiKeyError}
          </div>
        )}
        
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="apiKey" className="block text-sm font-medium text-gray-700 mb-1">
              API Key
            </label>
            <input
              type="password"
              id="apiKey"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded"
              placeholder="sk-..."
              required
            />
          </div>
          
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-white bg-blue-600 rounded hover:bg-blue-700"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

/**
 * StrategyPage component
 * 
 * Displays a complete strategy based on questionnaire answers
 */
function StrategyPage() {
  // Strategy state
  const [strategy, setStrategy] = useState(null);
  const [stepByStepPlan, setStepByStepPlan] = useState(null);
  const [recommendations, setRecommendations] = useState(null);
  const [generationTimestamp, setGenerationTimestamp] = useState(null);
  
  // AI model selection
  const [selectedModel, setSelectedModel] = useState('gemini-1.5-pro');
  const [openaiApiKey, setOpenaiApiKey] = useState('');
  const [showApiKeyDialog, setShowApiKeyDialog] = useState(false);
  const [apiKeyError, setApiKeyError] = useState(null);
  
  // Custom prompts from Prompt Management
  const [customPrompts, setCustomPrompts] = useState(null);
  
  // Company profile
  const [companyProfile, setCompanyProfile] = useState(null);
  
  // UI state
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStage, setGenerationStage] = useState('');
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('strategy');
  
  // Response data
  const [availableResponses, setAvailableResponses] = useState([]);
  const [selectedResponses, setSelectedResponses] = useState([]);
  const [responsesByType, setResponsesByType] = useState({});
  
  // Router hooks
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  // Load custom prompts and company profile from localStorage
  useEffect(() => {
    const savedPrompts = localStorage.getItem('savedPrompts');
    if (savedPrompts) {
      try {
        setCustomPrompts(JSON.parse(savedPrompts));
        console.log('Loaded custom prompts from localStorage');
      } catch (error) {
        console.error('Error loading custom prompts:', error);
      }
    }
    
    // Load company profile
    const savedProfile = localStorage.getItem('companyProfile');
    if (savedProfile) {
      try {
        setCompanyProfile(JSON.parse(savedProfile));
        console.log('Loaded company profile from localStorage');
      } catch (error) {
        console.error('Error loading company profile:', error);
      }
    }
  }, []);

  useEffect(() => {
    // Fetch all available questionnaire responses for the user
    const fetchUserResponses = async () => {
      if (!user) {
        setError('Please log in to view your marketing strategy.');
        setLoading(false);
        return;
      }
      
      setLoading(true);
      
      try {
        // Fetch all responses from Supabase for the current user
        const { data, error } = await supabase
          .from('questionnaire_responses')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });
        
        if (error) {
          console.error('Error fetching responses:', error);
          throw new Error('Failed to fetch responses from database: ' + error.message);
        }
        
        if (!data || data.length === 0) {
          setError('No questionnaire responses found. Please complete at least one questionnaire first.');
          setLoading(false);
          return;
        }
        
        // Validate the response data structure
        const validResponses = data.filter(response => {
          // Check if response has all required fields
          if (!response || !response.questionnaire_type || !response.responses) {
            console.warn('Invalid response data structure:', response);
            return false;
          }
          return true;
        });
        
        if (validResponses.length === 0) {
          setError('No valid questionnaire responses found. Please complete the questionnaires again.');
          setLoading(false);
          return;
        }
        
        console.log('Fetched responses:', validResponses);
        setAvailableResponses(validResponses);
        
        // Organize responses by type
        const byType = {};
        validResponses.forEach(response => {
          const type = response.questionnaire_type;
          if (!byType[type]) {
            byType[type] = [];
          }
          byType[type].push(response);
        });
        
        // Log response structure for debugging
        if (validResponses.length > 0) {
          const exampleResponse = validResponses[0];
          console.log('Response structure example:', {
            id: exampleResponse.id,
            questionnaire_type: exampleResponse.questionnaire_type,
            user_id: exampleResponse.user_id,
            created_at: exampleResponse.created_at,
            response_type: typeof exampleResponse.responses
          });
          
          // Log the actual response content for debugging
          console.log('Example response content:', exampleResponse.responses);
          
          // If responses is a string that looks like JSON, try to parse it
          if (typeof exampleResponse.responses === 'string' && 
              (exampleResponse.responses.startsWith('{') || exampleResponse.responses.startsWith('['))) {
            try {
              const parsedResponse = JSON.parse(exampleResponse.responses);
              console.log('Parsed example response:', parsedResponse);
            } catch (e) {
              console.warn('Failed to parse response string as JSON:', e);
            }
          }
        } else {
          console.log('No responses found');
        }
        
        setResponsesByType(byType);
        
        // Pre-select the most recent response of each type
        const preSelected = [];
        Object.keys(byType).forEach(type => {
          if (byType[type].length > 0 && byType[type][0] && byType[type][0].id) {
            // Get the most recent response of this type
            preSelected.push(byType[type][0].id);
          }
        });
        
        setSelectedResponses(preSelected);
        
        // Check if we have a strategy in sessionStorage from a recent generation
        const storedStrategy = sessionStorage.getItem('generated_strategy');
        if (storedStrategy) {
          try {
            const strategyData = JSON.parse(storedStrategy);
            setStrategy(strategyData.strategy);
            setStepByStepPlan(strategyData.plan);
            setRecommendations(strategyData.recommendations);
            setGenerationTimestamp(strategyData.timestamp);
          } catch (err) {
            console.error('Error parsing stored strategy:', err);
          }
        }
      } catch (err) {
        console.error('Error loading responses:', err);
        setError('Failed to load questionnaire responses: ' + err.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUserResponses();
  }, [user]);
  
  // Fetch all questionnaire responses from Supabase
  const fetchAllResponses = async (completionStatus) => {
    try {
      setLoading(true);
      const responses = {};
      let hasAnyResponses = false;
      
      // Only fetch responses for completed questionnaires
      for (const [type, completed] of Object.entries(completionStatus)) {
        if (completed) {
          const { data, error } = await getQuestionnaireResponses(type, true); // true = user-specific
          
          if (error) {
            console.error(`Error fetching ${type} responses:`, error);
            // Try localStorage as fallback
            const storedData = localStorage.getItem(`questionnaire_responses_${type}`);
            if (storedData) {
              responses[type] = JSON.parse(storedData);
              hasAnyResponses = true;
            }
          } else if (data && data.length > 0) {
            // Use the most recent response
            responses[type] = {
              questionnaire: data[0].questionnaire_name,
              responses: data[0].responses,
              timestamp: data[0].created_at
            };
            hasAnyResponses = true;
          }
        }
      }
      
      if (!hasAnyResponses) {
        setError('No questionnaire responses found. Please complete at least one questionnaire first.');
        setLoading(false);
        return;
      }
      
      setAllResponses(responses);
      generateStrategy(responses);
    } catch (err) {
      console.error('Error fetching responses:', err);
      setError('Failed to load questionnaire responses. Please try again.');
      setLoading(false);
    }
  };
  
  // Function to make direct client-side OpenAI API calls
  const callOpenAIDirectly = async (prompt, model) => {
    if (!openaiApiKey) {
      setShowApiKeyDialog(true);
      throw new Error('OpenAI API key is required');
    }
    
    try {
      console.log(`Making direct OpenAI API call with model: ${model}`);
      
      const openai = new OpenAI({
        apiKey: openaiApiKey,
        dangerouslyAllowBrowser: true
      });
      
      const response = await openai.chat.completions.create({
        model: model,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        max_tokens: 2000
      });
      
      return response;
    } catch (error) {
      console.error('Error calling OpenAI directly:', error);
      
      // Handle API key errors
      if (error.status === 401) {
        setApiKeyError('Invalid API key. Please check your OpenAI API key and try again.');
        setShowApiKeyDialog(true);
      }
      
      throw error;
    }
  };
  
  // Generate strategy based on selected questionnaire responses
  const generateStrategy = async () => {
    if (selectedResponses.length === 0) {
      setError('Please select at least one questionnaire response to generate a strategy.');
      return;
    }
    
    try {
      setGenerating(true);
      setGenerationProgress(0);
      setGenerationStage('Preparing questionnaire data');
      setError('');
      sessionStorage.removeItem('generated_strategy');
      
      // Reset state
      setStrategy('');
      setStepByStepPlan('');
      setRecommendations('');
      
      console.log('Generating new strategy, cleared previous cached data');
      
      // Get the selected response objects
      const selectedResponseObjects = availableResponses.filter(response => {
        // Make sure response has an id and it's in the selectedResponses array
        return response && response.id && selectedResponses.includes(response.id);
      });
      
      if (selectedResponseObjects.length === 0) {
        throw new Error('No valid responses found with the selected IDs');
      }
      
      // Format responses for the API
      const formattedResponses = {};
      selectedResponseObjects.forEach(response => {
        if (!response.questionnaire_type || !response.responses) {
          console.warn('Skipping invalid response:', response);
          return;
        }
        
        const questionnaireName = getQuestionnaireNameFromType(response.questionnaire_type);
        
        // Parse responses if they're stored as a JSON string
        let parsedResponses;
        if (typeof response.responses === 'string') {
          try {
            parsedResponses = JSON.parse(response.responses);
          } catch (e) {
            console.warn('Failed to parse responses JSON string:', e);
            parsedResponses = { 'Raw Response': response.responses };
          }
        } else if (typeof response.responses === 'object') {
          parsedResponses = response.responses;
        } else {
          console.warn('Unexpected response format:', typeof response.responses);
          parsedResponses = { 'Raw Response': String(response.responses) };
        }
        
        formattedResponses[questionnaireName] = parsedResponses;
        
        // Log the parsed responses for debugging
        console.log(`Parsed responses for ${questionnaireName}:`, parsedResponses);
      });
      
      // Check if we have any valid responses to process
      if (Object.keys(formattedResponses).length === 0) {
        throw new Error('No valid questionnaire data found in the selected responses');
      }
      
      console.log('Generating strategy with responses:', formattedResponses);

      // Helper function to get prompt (custom or default) and add company profile
      const getPrompt = (promptKey, defaultPrompt) => {
        let prompt;
        
        // Format the responses in a more readable way for the AI model
        const formattedResponsesText = () => {
          console.log('Formatting responses:', JSON.stringify(formattedResponses, null, 2));
          
          return Object.entries(formattedResponses)
            .map(([questionnaireType, responses]) => {
              // Start with the questionnaire type as a heading
              let responseText = `## ${questionnaireType} Questionnaire\n\n`;
              
              // Format each question and answer
              if (typeof responses === 'object' && responses !== null) {
                // Handle case where responses might be an array
                if (Array.isArray(responses)) {
                  responses.forEach((item, index) => {
                    if (typeof item === 'object' && item !== null) {
                      // If array contains objects, format each key-value pair
                      Object.entries(item).forEach(([key, value]) => {
                        // Properly stringify objects and arrays
                        const formattedValue = typeof value === 'object' && value !== null
                          ? JSON.stringify(value)
                          : value;
                        responseText += `**Question ${index + 1} - ${key}:** ${formattedValue}\n\n`;
                      });
                    } else {
                      // If array contains primitive values
                      responseText += `**Item ${index + 1}:** ${item}\n\n`;
                    }
                  });
                } else {
                  // Handle case where responses is a regular object
                  Object.entries(responses).forEach(([key, value]) => {
                    // Properly stringify objects and arrays
                    const formattedValue = typeof value === 'object' && value !== null
                      ? JSON.stringify(value)
                      : value;
                    responseText += `**${key}:** ${formattedValue}\n\n`;
                  });
                }
              }
              
              console.log(`Formatted ${questionnaireType} responses:`, responseText);
              return responseText;
            }).join('');
        };
        
        // Check if we have custom prompts
        if (customPrompts && customPrompts[promptKey]) {
          prompt = customPrompts[promptKey];
          console.log(`Using custom ${promptKey} prompt`);
        } else {
          prompt = defaultPrompt;
          console.log(`Using default ${promptKey} prompt`);
        }
        
        // Add company profile information if available
        if (companyProfile) {
          prompt = prompt.replace('{COMPANY_PROFILE}', `\n## Company Profile\n${Object.entries(companyProfile)
            .map(([key, value]) => `**${key}:** ${value}`)
            .join('\n')}\n`);
        } else {
          prompt = prompt.replace('{COMPANY_PROFILE}', '');
        }
        
        // Add questionnaire responses
        prompt = prompt.replace('{QUESTIONNAIRE_RESPONSES}', formattedResponsesText());
        
        return prompt;
        };
        
        // Define default prompts
        const defaultMarketingStrategyPrompt = `Generate a comprehensive marketing strategy based on the following questionnaire responses: {QUESTIONNAIRE_RESPONSES}. 

You are a world-class marketing strategist with expertise in developing effective marketing plans across various industries. Create a detailed marketing strategy that addresses the specific needs, challenges, and opportunities identified in the questionnaire responses.

{COMPANY_PROFILE}

Your strategy should include the following sections, each with detailed and actionable content:
1. Executive Summary - A concise overview of the entire strategy
2. Market Analysis - Industry trends, competitive landscape, and market opportunities
3. Target Audience - Detailed customer personas with demographics, psychographics, and behavioral patterns
4. Positioning Strategy - Unique value proposition and brand positioning
5. Marketing Channels - Specific channels to reach the target audience with budget allocation percentages
6. Messaging Framework - Key messages, tone, and communication style
7. Content Strategy - Types of content to create for each stage of the customer journey
8. Budget Allocation - Detailed breakdown of marketing spend across channels and initiatives
9. Timeline - Phased approach with key milestones
10. Success Metrics - KPIs and measurement framework

Format your response using markdown with clear headings, bullet points, and numbered lists where appropriate. Be specific, practical, and data-driven in your recommendations.`;
        
        const defaultImplementationPlanPrompt = `Based on the following questionnaire responses: {QUESTIONNAIRE_RESPONSES},

You are an experienced marketing implementation specialist. Create a detailed, actionable implementation plan for executing the marketing strategy that addresses the specific needs and challenges identified in the questionnaire responses.

{COMPANY_PROFILE}

Your implementation plan should include:

1. Immediate Actions (Next 30 Days)
   - List specific tasks with owners, deadlines, and required resources
   - Include quick wins that can show immediate results

2. Short-term Actions (1-3 Months)
   - Key initiatives to build momentum
   - Required team structure and responsibilities
   - Technology and tools needed

3. Medium-term Actions (3-6 Months)
   - Scaling successful initiatives
   - Performance review processes
   - Optimization strategies

4. Long-term Actions (6-12 Months)
   - Strategic expansion opportunities
   - Advanced measurement and analytics implementation

For each phase, include:
- Specific tasks and subtasks
- Resource requirements (people, budget, tools)
- Dependencies between tasks
- Risk factors and mitigation strategies
- Success criteria for each milestone

Format your response using markdown with clear headings, numbered lists, and bullet points for readability. Be practical, specific, and focused on execution.`;

        const defaultRecommendationsPrompt = `Based on the following questionnaire responses and company profile, provide key marketing recommendations and actionable insights.

{COMPANY_PROFILE}

{QUESTIONNAIRE_RESPONSES}

Format your response with clear sections covering:

1. Key Messaging and Positioning
2. Effective Marketing Channels
3. Content Strategy Recommendations
4. Budget Allocation Guidance
5. Performance Metrics to Track

Be specific, actionable, and data-driven in your recommendations.`;
          
          // For testing without API calls
          const useMockData = false;
          
          // Initialize these variables at the function level so they're accessible throughout
          let strategyText = '', planText = '', recommendationsText = '';
          
          if (useMockData) {
            console.log('Using mock data for strategy generation (API server may not be running)...');
            
            // Mock data for development/testing
            strategyText = `# Marketing Strategy

## Executive Summary
Based on the questionnaire responses, we recommend a digital-first approach focusing on social media engagement and content marketing to reach the identified target audience of young professionals and tech enthusiasts.

## Market Analysis
The market shows strong potential for growth in the digital space, with competitors primarily focusing on traditional channels. This presents an opportunity to differentiate through innovative digital marketing approaches.

## Target Audience
- Young professionals (25-40)
- Tech enthusiasts
- Urban dwellers with disposable income
- Education level: College degree or higher

## Positioning Strategy
Position the brand as an innovative, premium solution that understands the modern consumer's needs and lifestyle.`;
            
            planText = `# Implementation Plan

## Immediate Actions (Next 30 Days)
1. Audit existing marketing materials and digital presence
2. Set up analytics tracking for all digital channels
3. Develop content calendar for next 90 days
4. Identify key influencers in the target market

## Short-term Actions (1-3 Months)
1. Launch refreshed social media presence
2. Begin content marketing campaign
3. Implement SEO optimizations
4. Initiate partnerships with 2-3 key influencers`;
            
            recommendationsText = `# Marketing Recommendations

## Key Messaging and Positioning
- Focus on innovation and premium quality
- Emphasize how the product/service solves specific pain points
- Use language that resonates with tech-savvy professionals

## Effective Marketing Channels
- Instagram and LinkedIn as primary social platforms
- Content marketing through medium-length blog posts
- Email marketing for nurturing leads
- Targeted digital advertising on professional networks`;
            
            // Log the prompts that would have been used
            console.log('Would have used these prompts:');
            console.log('Marketing Strategy:', getPrompt('marketingStrategy', defaultMarketingStrategyPrompt).substring(0, 100) + '...');
            console.log('Implementation Plan:', getPrompt('implementationPlan', defaultImplementationPlanPrompt).substring(0, 100) + '...');
            console.log('Recommendations:', getPrompt('recommendations', defaultRecommendationsPrompt).substring(0, 100) + '...');
            
            // Simulate network delay
            await new Promise(resolve => setTimeout(resolve, 2000));
          } else {
            // Call the API to generate the main strategy
            console.log('Calling API for main strategy...');
            
            // Get the prompts
            const marketingStrategyPrompt = getPrompt('marketingStrategy', defaultMarketingStrategyPrompt);
            const implementationPlanPrompt = getPrompt('implementationPlan', defaultImplementationPlanPrompt);
            const recommendationsPrompt = getPrompt('recommendations', defaultRecommendationsPrompt);
            
            // Debug: Log the complete prompts to see if questionnaire responses are included
            console.log('DEBUG - Marketing Strategy Prompt:', marketingStrategyPrompt);
            console.log('DEBUG - Implementation Plan Prompt:', implementationPlanPrompt);
            console.log('DEBUG - Recommendations Prompt:', recommendationsPrompt);
            
            // Determine which API endpoint to use based on the selected model
            const isO1Model = selectedModel.startsWith('o1-');
            const isOpenAI = selectedModel.startsWith('gpt') && !isO1Model; // Regular GPT models only
            const isDeepSeek = selectedModel.startsWith('deepseek');
            const isAnthropic = selectedModel.startsWith('claude');
            const isGroq = selectedModel.includes('llama') || selectedModel.includes('mixtral') || selectedModel.includes('groq');

            // Get the appropriate API endpoint and ensure model name is compatible
            let apiEndpoint;
            let apiModel = selectedModel; // By default, use the selected model name

            if (isO1Model) {
              // For o1 models, use Railway backend which handles the special parameters
              apiEndpoint = config.endpoints.openai.ask;
              console.log('Using Railway backend for o1 model:', apiEndpoint, 'with model:', apiModel);
            } else if (isOpenAI) {
              // For regular OpenAI models, use the OpenAI endpoint and keep the model name as is
              apiEndpoint = config.endpoints.openai.ask;
              console.log('Using OpenAI API endpoint:', apiEndpoint, 'with model:', apiModel);
            } else if (isDeepSeek) {
              // For DeepSeek models, use the DeepSeek endpoint
              apiEndpoint = config.endpoints.deepseek.ask;
              console.log('Using DeepSeek API endpoint:', apiEndpoint, 'with model:', apiModel);
            } else if (isAnthropic) {
              // For Anthropic models, use the Anthropic endpoint
              apiEndpoint = config.endpoints.anthropic.ask;
              console.log('Using Anthropic API endpoint:', apiEndpoint, 'with model:', apiModel);
            } else if (isGroq) {
              // For Groq models, use the Groq endpoint
              apiEndpoint = config.endpoints.groq.ask;
              console.log('Using Groq API endpoint:', apiEndpoint, 'with model:', apiModel);
            } else {
              // For all other models, use the Gemini endpoint
              apiEndpoint = config.endpoints.gemini.ask;

              // Ensure the model name is compatible with Gemini API
              if (!selectedModel.startsWith('gemini')) {
                apiModel = 'gemini-1.5-pro'; // Default to Gemini 1.5 Pro for non-Gemini models
                console.log('Converting model name to compatible Gemini model:', apiModel);
              }

              console.log('Using Gemini API endpoint:', apiEndpoint, 'with model:', apiModel);
            }
            
            // Configure axios with timeout and retry logic
            const axiosConfig = {
              timeout: 30000, // 30 second timeout
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              }
            };
            
            let strategyResponse, planResponse, recommendationsResponse;
            
            try {
              
              // Step 1: Generate the main marketing strategy
              setGenerationStage('Generating marketing strategy');
              setGenerationProgress(10);
              
              if (isOpenAI && !isO1Model) {
                // Use client-side OpenAI call for regular GPT models only
                console.log('Making client-side OpenAI call for strategy');
                strategyResponse = await callOpenAIDirectly(marketingStrategyPrompt, selectedModel);
                strategyText = strategyResponse.choices[0].message.content;
              } else {
                // Use server API endpoint (Railway backend for o1 models, other backends for other models)
                console.log(`Making server API call to ${apiEndpoint} for strategy`);
                strategyResponse = await axios.post(apiEndpoint, {
                  prompt: marketingStrategyPrompt,
                  model: apiModel,
                  thinking_mode: isO1Model // Enable thinking mode for o1 models
                }, axiosConfig);

                // Debug: Log the full response to see what we're getting
                console.log('Strategy response from server:', strategyResponse);
                console.log('Strategy response data:', strategyResponse.data);
                console.log('Strategy response data keys:', Object.keys(strategyResponse.data || {}));
                console.log('Strategy response data type:', typeof strategyResponse.data);

                // More explicit text extraction with detailed logging
                console.log('Checking response fields:');
                console.log('- strategyResponse.data.text:', typeof strategyResponse.data.text, strategyResponse.data.text ? strategyResponse.data.text.substring(0, 100) + '...' : 'FALSY');
                console.log('- strategyResponse.data.answer:', typeof strategyResponse.data.answer, strategyResponse.data.answer ? strategyResponse.data.answer.substring(0, 100) + '...' : 'FALSY');
                console.log('- strategyResponse.data.response:', typeof strategyResponse.data.response, strategyResponse.data.response ? strategyResponse.data.response.substring(0, 100) + '...' : 'FALSY');

                strategyText = strategyResponse.data.text || strategyResponse.data.answer || strategyResponse.data.response || '';
                console.log('Extracted strategy text:', strategyText ? strategyText.substring(0, 100) + '...' : 'EMPTY');

                // If no text found, log all available fields
                if (!strategyText) {
                  console.log('No text found in expected fields. Full response data:', JSON.stringify(strategyResponse.data, null, 2));
                }
              }
              
              setStrategy(strategyText);
              setGenerationProgress(40);
              
              // Step 2: Generate the implementation plan
              setGenerationStage('Creating implementation plan');
              
              if (isOpenAI && !isO1Model) {
                // Use client-side OpenAI call for regular GPT models only
                console.log('Making client-side OpenAI call for implementation plan');
                planResponse = await callOpenAIDirectly(implementationPlanPrompt, apiModel);
                planText = planResponse.choices[0].message.content;
              } else {
                // Use server API endpoint (Railway backend for o1 models, other backends for other models)
                console.log(`Making server API call to ${apiEndpoint} for implementation plan`);
                planResponse = await axios.post(apiEndpoint, {
                  prompt: implementationPlanPrompt,
                  model: apiModel,
                  thinking_mode: isO1Model // Enable thinking mode for o1 models
                }, axiosConfig);

                // Debug: Log the response
                console.log('Plan response data:', planResponse.data);
                planText = planResponse.data.text || planResponse.data.answer || planResponse.data.response || '';
                console.log('Extracted plan text:', planText ? planText.substring(0, 100) + '...' : 'EMPTY');
              }
              
              setStepByStepPlan(planText);
              setGenerationProgress(70);
              
              // Step 3: Generate recommendations
              setGenerationStage('Finalizing recommendations');
              
              if (isOpenAI && !isO1Model) {
                // Use client-side OpenAI call for regular GPT models only
                console.log('Making client-side OpenAI call for recommendations');
                recommendationsResponse = await callOpenAIDirectly(recommendationsPrompt, apiModel);
                recommendationsText = recommendationsResponse.choices[0].message.content;
              } else {
                // Use server API endpoint (Railway backend for o1 models, other backends for other models)
                console.log(`Making server API call to ${apiEndpoint} for recommendations`);
                recommendationsResponse = await axios.post(apiEndpoint, {
                  prompt: recommendationsPrompt,
                  model: apiModel,
                  thinking_mode: isO1Model // Enable thinking mode for o1 models
                }, axiosConfig);

                // Debug: Log the response
                console.log('Recommendations response data:', recommendationsResponse.data);
                recommendationsText = recommendationsResponse.data.text || recommendationsResponse.data.answer || recommendationsResponse.data.response || '';
                console.log('Extracted recommendations text:', recommendationsText ? recommendationsText.substring(0, 100) + '...' : 'EMPTY');
              }
              
              setRecommendations(recommendationsText);
              setGenerationProgress(100);
              setGenerationStage('Strategy generation complete');
              
              // Save to sessionStorage for caching
              const generatedData = {
                strategy: strategyText,
                plan: planText,
                recommendations: recommendationsText,
                timestamp: new Date().toISOString(),
                model: selectedModel,
                apiModel: apiModel // Store the actual model used for the API call
              };
              
              sessionStorage.setItem('generated_strategy', JSON.stringify(generatedData));
              setGenerationTimestamp(new Date().toISOString());
              
              console.log('Strategy generation complete');
            } catch (apiCallError) {
              console.error('Error during API calls:', apiCallError);
              throw apiCallError; // Re-throw to be caught by the outer try-catch
            }
          }
          
          // Debug: Log final extracted texts
          console.log('Final extracted texts:');
          console.log('- strategyText:', strategyText ? 'HAS CONTENT' : 'EMPTY');
          console.log('- planText:', planText ? 'HAS CONTENT' : 'EMPTY');
          console.log('- recommendationsText:', recommendationsText ? 'HAS CONTENT' : 'EMPTY');

          if (strategyText) {
            // Generate a timestamp for the strategy generation
            const timestamp = new Date().toISOString();

            // Update state with the generated content and timestamp
            setStrategy(strategyText);
            setStepByStepPlan(planText);
            setRecommendations(recommendationsText);
            setGenerationTimestamp(timestamp);

            // Set progress to 100% when complete
            setGenerationProgress(100);
            setGenerationStage('Strategy generation complete!');

            // Store in sessionStorage for persistence
            sessionStorage.setItem('generated_strategy', JSON.stringify({
              strategy: strategyText,
              plan: planText,
              recommendations: recommendationsText,
              timestamp: timestamp,
              responseIds: selectedResponses,
              // Also store the prompts used for reference
              prompts: {
                marketingStrategy: customPrompts?.marketingStrategy || 'default',
                implementationPlan: customPrompts?.implementationPlan || 'default',
                recommendations: customPrompts?.recommendations || 'default'
              }
            }));
          } else {
            console.log('ERROR: No strategy text found. Setting error state.');
            setGenerationProgress(0);
            setError('Failed to generate strategy. The API response did not contain the expected data format.');
          }
      } catch (apiError) {
        console.error('API call failed:', apiError);
        
        // Handle different types of errors
        if (apiError.code === 'ECONNABORTED') {
          setError('The request timed out. The server might be busy or unavailable. Please try again later.');
        } else if (apiError.response) {
          // The server responded with a status code outside the 2xx range
          setError(`Server error: ${apiError.response.status} ${apiError.response.statusText}. Please contact support.`);
          console.log('Error response data:', apiError.response.data);
        } else if (apiError.request) {
          // The request was made but no response was received
          setError('No response received from the server. Please check your internet connection and try again.');
        } else {
          // Something else happened in setting up the request
          setError(`Network Error: ${apiError.message}. Please check if the API server is running.`);
        }
        
        // Log the error for debugging
        console.error('Error generating strategy:', apiError);
        
        // Set a generic error message if one hasn't been set already
        if (!apiError.isAxiosError) {
          setError('An error occurred while generating the strategy: ' + (apiError.message || 'Unknown error'));
        }
      } finally {
        setGenerating(false);
      }
  };
  
  // Clear cached strategy data
  const clearCachedStrategy = () => {
    // Remove from sessionStorage
    sessionStorage.removeItem('generated_strategy');
    
    // Clear state
    setStrategy('');
    setStepByStepPlan('');
    setRecommendations('');
    setGenerationTimestamp(null);
    
    console.log('Manually cleared cached strategy data');
  };
  
  // Handle response selection
  const toggleResponseSelection = (responseId) => {
    if (!responseId) return; // Skip if responseId is undefined or null
    
    setSelectedResponses(prev => {
      if (prev.includes(responseId)) {
        return prev.filter(id => id !== responseId);
      } else {
        return [...prev, responseId];
      }
    });
  };
  
  // Format the strategy text with proper styling
  const formatStrategyText = (text, isImplementationPlan = false) => {
    if (!text) return <p>No content available</p>;
    
    // Clean up the text to completely remove markdown characters
    let cleanedText = text
      // Remove ** characters completely
      .replace(/\*\*([^*]+)\*\*/g, '$1')
      // Remove * characters completely
      .replace(/\*([^*]+)\*/g, '$1')
      // Remove # characters at the beginning of lines
      .replace(/^#\s+(.+)$/gm, '$1')
      // Remove ## characters at the beginning of lines
      .replace(/^##\s+(.+)$/gm, '$1')
      // Remove ### characters at the beginning of lines
      .replace(/^###\s+(.+)$/gm, '$1');
      
    // Detect headings and important content based on context clues
    const detectHeadings = (lines) => {
      // Process lines to identify potential headings
      let stepCounter = 0;
      
      return lines.map((line, index) => {
        // Empty lines become breaks
        if (line.trim() === '') {
          return { type: 'break', content: '', index };
        }
        
        // Short lines (less than 60 chars) that are all uppercase or end with a colon are likely headings
        if (line.length < 60 && (line === line.toUpperCase() || line.trim().endsWith(':')) && line.trim().length > 0) {
          return { type: 'heading1', content: line, index };
        }
        
        // For implementation plan, detect steps with more precision
        if (activeTab === 'plan') {
          // Check for lines that look like steps (start with "Step X" or just a number)
          const stepMatch = line.match(/^(?:Step\s*[#:]?\s*(\d+)|^\s*(\d+)[.):])\s*(.+)$/i);
          
          if (stepMatch) {
            // Extract the step number and content
            const stepNum = stepMatch[1] || stepMatch[2] || ++stepCounter;
            const stepContent = stepMatch[3] || stepMatch[0].replace(/^\s*\d+[.):]*\s*/, '');
            
            return { 
              type: 'step', 
              number: parseInt(stepNum, 10), 
              content: stepContent, 
              index 
            };
          }
        }
        
        // Lines that start with numbers followed by a period or parenthesis are likely list items
        if (/^\d+[.)\s]/.test(line) || line.startsWith('- ') || line.startsWith('• ')) {
          return { type: 'listItem', content: line.replace(/^\d+[.)\s]|^-\s|^•\s/, ''), index };
        }
        
        // Default to paragraph
        return { type: 'paragraph', content: line, index };
      });
    };
    
    const processedLines = detectHeadings(cleanedText.split('\n'));
    
    return (
      <div className="markdown-content">
        {processedLines.map(line => {
          switch (line.type) {
            case 'heading1':
            default:
              return <p key={line.index} className="mb-2">{line.content}</p>;
          }
        })}
      </div>
    );
  };
  
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown Date';
    
    try {
      const date = new Date(dateString);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }
      
      return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric'
      }) + ' at ' + date.toLocaleTimeString('en-US');
    } catch (err) {
      return 'Date Error';
    }
  };
  
  // Handle API key submission
  const handleApiKeySave = (apiKey) => {
    setOpenaiApiKey(apiKey);
    setShowApiKeyDialog(false);
    setApiKeyError(null);
    // Save API key to localStorage
    localStorage.setItem('openai_api_key', apiKey);
  };

  // Load OpenAI API key from localStorage on component mount
  useEffect(() => {
    const savedApiKey = localStorage.getItem('openai_api_key');
    if (savedApiKey) {
      setOpenaiApiKey(savedApiKey);
    }
  }, []);

  return (
    <div className="container mx-auto px-4 py-2 max-w-6xl bg-gray-50">
      {/* API Key Dialog */}
      <ApiKeyDialog
        isOpen={showApiKeyDialog}
        onClose={() => {
          setShowApiKeyDialog(false);
          // If no API key and user cancels, reset to Gemini model
          if (!openaiApiKey && (selectedModel.startsWith('gpt') || selectedModel.startsWith('o1-'))) {
            setSelectedModel('gemini-1.5-pro');
          }
        }}
        onSave={handleApiKeySave}
        apiKeyError={apiKeyError}
      />
      <h1 className="text-3xl font-bold mb-3 bg-purple-100 p-3 rounded-lg text-purple-800 text-center border-2 border-purple-300">Generate your Marketing Strategy<span className="text-green-600 font-bold"></span></h1>
      
      {loading ? (
        <div className="flex flex-col items-center justify-center py-12 bg-white p-6 rounded-lg shadow-md">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-gray-600">Loading your questionnaire responses...</p>
        </div>
      ) : error ? (
        <div className="bg-red-50 p-6 rounded-lg border border-red-200 mb-6 shadow-md">
          <h3 className="font-semibold text-red-800 mb-2">Error</h3>
          <p className="text-red-700">{error}</p>
          <button 
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
            onClick={() => navigate('/market-research')}
          >
            Back to Questionnaires
          </button>
        </div>
      ) : (
        <div className="flex flex-col gap-3">
          {/* Horizontal Questionnaire selection panel */}
          <div className="w-full">
            {/* First Section: Questionnaire Selection */}
            <div className="bg-white p-4 rounded-lg shadow-md mb-4">
              <h2 className="text-xl font-semibold mb-3 text-blue-700">Select Questionnaire Responses</h2>
              <p className="text-gray-600 mb-4">Select at least one response from each questionnaire type for the most comprehensive strategy.</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.entries(responsesByType).map(([type, responses]) => (
                  <div key={type} className="mb-2">
                    <h3 className="font-medium text-gray-800 mb-2">{getQuestionnaireNameFromType(type)}</h3>
                    <div className="space-y-2">
                      {responses.map(response => (
                        <div 
                          key={response.id}
                          className="flex items-center p-2 border rounded-lg cursor-pointer transition-colors hover:bg-gray-50"
                          onClick={() => toggleResponseSelection(response.id)}
                        >
                          <input 
                            type="checkbox" 
                            checked={selectedResponses.includes(response.id)}
                            onChange={() => {}} // Handled by the div onClick
                            className="h-4 w-4 text-blue-600 rounded"
                          />
                          <div className="ml-3 overflow-hidden">
                            <p className="text-sm font-medium truncate">{formatDate(response.created_at)}</p>
                            <p className="text-xs text-gray-500">ID: {safeGetId(response)}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
              
              {selectedResponses.length > 0 && (
                <div className="mt-3 p-2 bg-green-50 border border-green-100 rounded text-sm text-green-700">
                  <span className="font-medium">{selectedResponses.length}</span> questionnaire response(s) selected
                </div>
              )}
            </div>
            
            {/* Second Section: Strategy Generation */}
            <div className="bg-white p-4 rounded-lg shadow-md mb-4">
              <h2 className="text-xl font-semibold mb-2 text-blue-700">Generate Marketing Strategy</h2>
              <p className="text-gray-600 mb-4">Create a customized marketing strategy based on your selected questionnaire responses</p>
              
              {/* AI Model Selection */}
              <div className="mb-4 p-3 bg-gray-50 rounded border border-gray-200">
                <h3 className="text-md font-medium mb-2 text-gray-700">Select AI Model</h3>
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                  <div className="w-full sm:w-auto">
                    <select
                      value={selectedModel}
                      onChange={(e) => {
                        const model = e.target.value;
                        setSelectedModel(model);
                        // Show API key dialog if OpenAI model is selected and no key is stored
                        if ((model.startsWith('gpt') || model.startsWith('o1-')) && !openaiApiKey) {
                          setShowApiKeyDialog(true);
                        }
                      }}
                      className="w-full sm:w-auto px-3 py-2 border border-gray-300 rounded text-sm"
                      disabled={generating}
                    >
                      <optgroup label="🧠 Thinking Models">
                        <option value="o1-preview">OpenAI o1-preview (Best Reasoning)</option>
                        <option value="o1-mini">OpenAI o1-mini (Fast Reasoning)</option>
                        <option value="claude-3-5-sonnet-20241022">Claude 3.5 Sonnet (Excellent Reasoning)</option>
                        <option value="deepseek-r1">DeepSeek R1 (Budget Reasoning)</option>
                      </optgroup>
                      <optgroup label="OpenAI">
                        <option value="gpt-4o">GPT-4o</option>
                        <option value="gpt-4-turbo">GPT-4 Turbo</option>
                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                      </optgroup>
                      <optgroup label="Google Gemini">
                        <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                        <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                        <option value="gemini-pro">Gemini Pro</option>
                      </optgroup>
                      <optgroup label="Anthropic">
                        <option value="claude-3-5-sonnet-20241022">Claude 3.5 Sonnet</option>
                        <option value="claude-3-opus-20240229">Claude 3 Opus</option>
                        <option value="claude-3-haiku-20240307">Claude 3 Haiku</option>
                      </optgroup>
                      <optgroup label="DeepSeek">
                        <option value="deepseek-chat">DeepSeek Chat</option>
                        <option value="deepseek-r1">DeepSeek R1 (Reasoning)</option>
                      </optgroup>
                      <optgroup label="Groq">
                        <option value="llama3-70b-8192">Llama 3 70B</option>
                        <option value="llama3-8b-8192">Llama 3 8B</option>
                        <option value="mixtral-8x7b-32768">Mixtral 8x7B</option>
                      </optgroup>
                    </select>
                  </div>
                  
                  <button
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition flex-shrink-0 disabled:bg-blue-300 disabled:cursor-not-allowed"
                    onClick={generateStrategy}
                    disabled={selectedResponses.length === 0 || generating}
                  >
                    {generating ? (
                      <div className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Generating...
                      </div>
                    ) : (
                      'Generate Strategy'
                    )}
                  </button>
                  
                  {selectedModel.startsWith('gpt') && openaiApiKey && (
                    <button
                      className="text-xs text-blue-600 hover:text-blue-800 underline"
                      onClick={() => setShowApiKeyDialog(true)}
                      disabled={generating}
                    >
                      Change API Key
                    </button>
                  )}
                </div>
              </div>
              
              {generationTimestamp && (
                <div className="text-sm text-gray-600 mb-4 p-2 bg-blue-50 rounded border border-blue-100">
                  <span className="font-medium">Strategy last generated:</span> {formatDate(generationTimestamp)}
                  {strategy && <span className="ml-2 text-xs text-blue-600">(using cached data)</span>}
                </div>
              )}
              
              {generating && (
                <div className="mt-4 space-y-2">
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>{generationStage}</span>
                    <span>{generationProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div 
                      className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-in-out" 
                      style={{ width: `${generationProgress}%` }}
                    ></div>
                  </div>
                  <div className="flex items-center justify-center text-xs text-gray-500 mt-1">
                    <div className="animate-spin h-3 w-3 border border-blue-600 rounded-full border-t-transparent mr-2"></div>
                    This may take a minute or two
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Strategy display panel - now full width */}
          <div className="w-full">
            {strategy ? (
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-6">
                  <h3 className="font-semibold text-blue-800 mb-2">Strategy Generated Successfully</h3>
                  <p className="text-blue-700">
                    Based on your questionnaire responses, we've created a comprehensive marketing strategy tailored to your business needs.
                  </p>
                </div>
                
                {/* Strategy Tabs */}
                <div className="mb-6">
                  <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8">
                      <button
                        className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'strategy' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                        onClick={() => setActiveTab('strategy')}
                      >
                        Complete Strategy
                      </button>
                      
                      {stepByStepPlan && (
                        <button
                          className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'plan' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                          onClick={() => setActiveTab('plan')}
                        >
                          Step-by-Step Plan
                        </button>
                      )}
                      
                      {recommendations && (
                        <button
                          className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'recommendations' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                          onClick={() => setActiveTab('recommendations')}
                        >
                          Recommendations
                        </button>
                      )}
                    </nav>
                  </div>
                </div>
                
                {/* Strategy Content based on active tab */}
                {activeTab === 'strategy' && (
                  <div className="strategy-content prose prose-blue max-w-none mb-12">
                    <h2 className="text-2xl font-bold text-blue-800 mb-4">Complete Marketing Strategy</h2>
                    {formatStrategyText(strategy)}
                  </div>
                )}
                
                {activeTab === 'plan' && stepByStepPlan && (
                  <div className="strategy-content prose prose-blue max-w-none mb-12">
                    <h2 className="text-2xl font-bold text-blue-800 mb-4">Step-by-Step Implementation Plan</h2>
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-6">
                      <p className="text-blue-700">
                        Follow this structured implementation plan to execute your marketing strategy effectively. Each step builds on the previous one for maximum impact.
                      </p>
                    </div>
                    {formatStrategyText(stepByStepPlan, true)}
                  </div>
                )}
                
                {activeTab === 'recommendations' && recommendations && (
                  <div className="strategy-content prose prose-blue max-w-none mb-12">
                    <h2 className="text-2xl font-bold text-blue-800 mb-4">Key Recommendations</h2>
                    {formatStrategyText(recommendations)}
                  </div>
                )}
                
                <div className="flex justify-end mt-8">
                  <button 
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
                    onClick={() => window.print()}
                  >
                    Print Strategy
                  </button>
                </div>
              </div>
            ) : (
              <div className="bg-white p-4 rounded-lg shadow-md mt-4">
                <div className="flex flex-col items-center justify-center">
                  <div className="w-32 h-32 mb-2 flex items-center justify-center bg-gray-100 rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <h2 className="text-lg font-semibold text-gray-700 mb-2">Generate Your Marketing Strategy</h2>
                  <p className="text-gray-600 text-center max-w-md mb-2">
                    1. Select questionnaire responses and click "Generate New Marketing Strategy" to create a customized strategy for your business.
                  </p>
                  <p className="text-sm text-gray-500 text-center max-w-md mb-2">
                    The more questionnaires you complete, the more comprehensive your strategy will be.
                  </p>
                </div>
                {!companyProfile && (
                  <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mt-4 max-w-md">
                    <p className="font-bold">Company Profile Not Found</p>
                    <p>
                      To enhance your marketing strategy with your company context, please 
                      <Link to="/company-profile" className="text-blue-600 underline ml-1">
                        complete your company profile
                      </Link>.
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Helper function to get readable questionnaire name from type
const getQuestionnaireNameFromType = (type) => {
  switch(type) {
    case 'consumerInsight': return 'Consumer Insights';
    case 'segmentation': return 'Market Segmentation';
    case 'trends': return 'Trend Analysis';
    case 'focusGroups': return 'Focus Groups';
    default: return 'Questionnaire';
  }
};

export default StrategyPage;
// Updated with thinking models: o1-preview, o1-mini, Claude 3.5 Sonnet, DeepSeek R1
