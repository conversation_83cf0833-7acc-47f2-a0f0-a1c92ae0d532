/**
 * Configuration for the application
 * This file manages environment-specific settings
 */

// Determine if we're in production based on the URL
const isProduction = window.location.hostname !== 'localhost';

// API base URL - use relative URLs in production for Vercel deployment
const API_BASE_URL = isProduction ? '/api' : 'http://localhost:3000/api';

export default {
  API_BASE_URL,
  endpoints: {
    // Questionnaire endpoints
    questionnaire: {
      list: `${API_BASE_URL}/questionnaire/list`,
      get: (id) => `${API_BASE_URL}/questionnaire/${id}`,
      save: `${API_BASE_URL}/questionnaire/save`,
      generate: `${API_BASE_URL}/questionnaire/generate`,
    },
    // YAML endpoints
    yaml: {
      list: `${API_BASE_URL}/yaml/list`,
    },
    // Response endpoints
    responses: {
      list: `${API_BASE_URL}/responses/list`,
      get: (id) => `${API_BASE_URL}/responses/${id}`,
      save: `${API_BASE_URL}/responses/save`,
      download: (id) => `${API_BASE_URL}/responses/download/${id}`,
    },
    // AI model endpoints
    gemini: {
      list: `${API_BASE_URL}/gemini/list`,
      ask: `${API_BASE_URL}/gemini`,
    },
    openai: {
      ask: `${API_BASE_URL}/openai`,
    },
    deepseek: {
      ask: `${API_BASE_URL}/deepseek`,
    }
  }
};
