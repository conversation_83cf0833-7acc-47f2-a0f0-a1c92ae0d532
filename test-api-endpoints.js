// Test script for API endpoints
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testEndpoint(name, endpoint, model, requestData) {
  console.log(`\n\n===== Testing ${name} API =====`);
  console.log(`Endpoint: ${endpoint}`);
  console.log(`Model: ${model}`);
  console.log(`Request data:`, requestData);
  
  try {
    console.log('Sending request...');
    const response = await axios.post(`${BASE_URL}${endpoint}`, requestData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return false;
  }
}

async function runTests() {
  console.log('Starting API endpoint tests...');
  
  // Test Gemini API - accepts both prompt and question
  const geminiResult = await testEndpoint('Gemini', '/api/gemini', 'gemini-1.5-pro', {
    prompt: 'Generate a short marketing tagline for a coffee shop',
    question: 'Generate a short marketing tagline for a coffee shop', // Adding both for redundancy
    model: 'gemini-1.5-pro'
  });
  
  // Test OpenAI API - requires question parameter
  const openaiResult = await testEndpoint('OpenAI', '/api/openai', 'gpt-3.5-turbo', {
    question: 'Generate a short marketing tagline for a coffee shop',
    model: 'gpt-3.5-turbo'
  });
  
  // Test DeepSeek API
  const deepseekResult = await testEndpoint('DeepSeek', '/api/deepseek', 'deepseek-chat', {
    prompt: 'Generate a short marketing tagline for a coffee shop',
    model: 'deepseek-chat'
  });
  
  
  // Summary
  console.log('\n\n===== Test Results =====');
  console.log(`Gemini API: ${geminiResult ? 'SUCCESS' : 'FAILED'}`);
  console.log(`OpenAI API: ${openaiResult ? 'SUCCESS' : 'FAILED'}`);
  console.log(`DeepSeek API: ${deepseekResult ? 'SUCCESS' : 'FAILED'}`);
}

runTests();
